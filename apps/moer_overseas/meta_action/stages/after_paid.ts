import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class AfterPaid extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return await DataService.isPaidSystemCourse(chatId)
  }

  async getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return null
  }

  async getMetaAction(): Promise<Record<string, string>> { return MetaActions.afterPaid }

  async getThinkPrompt(): Promise<string> { return ThinkPrompt.afterPaid }
}
