import { chatStateStoreClient } from '../../service/base_instance'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { PostAction } from './post_action'

export class AfterBonding extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const nodeCount = await chatStateStoreClient.getNodeCount(chatId, 'FreeTalk')
    return nodeCount > 15
  }
  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '提醒完成小讲堂': PostAction.sendPreCourseLink,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> { return Promise.resolve(MetaActions.AfterBonding) }

  getThinkPrompt(): Promise<string> { return Promise.resolve(ThinkPrompt.afterBonding) }
}