import { Node } from '../../../workflow/nodes/types'
import { sleep } from 'lib/schedule/schedule'
import { IChattingFlag } from '../../../state/user_flags'
import { IMoerUserInfo, MoerOverseasAPI } from 'model/moer_overseas_api/moer_overseas_api'
import { getUserId } from 'config/chat_id'
import logger from 'model/logger/logger'
import {
  yCloudMessageSender
} from '../../../service/instance'
import { UserLanguage } from '../../../helper/language/user_language_verify'
import { DataService } from '../../../helper/getter/get_data'
import { startTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { calSopTime } from '../../../visualized_sop/visualized_sop_processor'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../../../schedule/type'
import { chatStateStoreClient, chatDBClient, chatDBCommonClient } from '../../../service/base_instance'

export class SendWelcomeMessageTask {


  public static async sendWelcomeMessage(chatId: string, phoneNumber: string, name:string) {

    await sleep(2 * 60 * 1000)

    try {
      await this.newCourseUser(chatId, phoneNumber, name)
      const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
      if (!state.is_send_welcome_message) {
        await this.sendMessage(chatId)
        await chatStateStoreClient.update(chatId, {
          state: <IChattingFlag> {
            is_send_welcome_message:true
          }
        })


        await SilentReAsk.schedule(TaskName.intentionQuery, chatId, 10 * 60 * 1000, {}, {
          auto_retry: true,
          independent: true
        })

      }
    } catch (e) {
      logger.warn(`客户手机号绑定失败，phoneNumber:${phoneNumber}`, e)
    }

  }


  public static async newCourseUser(chatId: string, phoneNumber: string, name:string) {

    phoneNumber = phoneNumber.replace(/[^0-9]/g, '')

    await chatStateStoreClient.update(chatId, {
      nextStage: Node.FreeTalk
    })


    await chatDBClient.updatePhoneNumber(chatId, phoneNumber)
    if (name != '') {
      await chatDBCommonClient.updateContact(chatId, getUserId(chatId), name)
    }
    const moerData = await MoerOverseasAPI.getUserByPhone(phoneNumber)
    const email = moerData.email
    const moerId = moerData.id.toString()
    await chatDBClient.updateMoerId(chatId, moerId)
    await chatDBClient.updateEmail(chatId, email)
    await this.updateLanguage(chatId, moerData)
    await this.updateCourseNo(chatId, moerData)
    await this.updateMobileAndNationCode(chatId, moerData)
    await startTasks(chatStateStoreClient, 'moer_overseas', getUserId(chatId), chatId, calSopTime)

    //绑定时区信息
    // await chatStateStoreClient.update(chatId, {
    //   state: <IChattingFlag>{
    //     time_zone:'UTC+8:00'
    //   }
    // })
  }

  public static async startIntentionQuery(chatId: string, userId: string) {
    const isSendIntentionQuery = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_send_user_query
    const language = await UserLanguage.getLanguage(chatId)



    if (!isSendIntentionQuery) {

      await sleep(2000)

      if (language === UserLanguage.Language_EN) {
        await yCloudMessageSender.sendById({
          chat_id: chatId,
          ai_msg: `Also, from now on, I'll be your personal meditation coach throughout the training camp. Let’s chat a bit about youself so I can tailor support to what you need most~

🙆🏻‍♀️ Your current life role?
1- Career go-getter
2- Family manager
3- Retired & growing
4- Spiritual practitioner

🐣 Your meditation experience level?
5- Complete beginner
6- Some exposure
7- Have basic foundation

🎯 What’s your top focus right now?
8- Stress & emotional balance
9- Focus improvement
10- Sleep quality
11- Wealth energy
12- Relationships
13- Spiritual growth

——————
Just reply with numbers, e.g., 1-5-8`,
          type: 'text'
        })
      } else {
        await yCloudMessageSender.sendById({
          chat_id: chatId,
          ai_msg: `另外，從現在開始，我就是你訓練營期間的專屬冥想教練了，可以簡單和我聊聊你的情況，看看我能幫到什麼~

🙆🏻‍♀️您目前的生活角色？
1-職場奮鬥者
2-家庭管理者
3-退休精進者
4-修行者

🐣您的冥想經驗值？
5-純小白
6-接觸過
7-有基礎

🎯最想點亮的人生議題
8-情緒減壓
9-專注提升
10-睡眠改善
11-財富能量
12-親密關係
13-靈性成長

——————
回覆我數字就好，例如：1-5-8`,
          type: 'text'
        })
      }



      await chatStateStoreClient.update(chatId, {
        state: <IChattingFlag>{
          is_send_user_query: true } })

      // 15min不回复
      // await SilentReAsk.schedule(chatId, async () => {
      //   await yCloudMessageSender.sendById({
      //     user_id: userId,
      //     chat_id: chatId,
      //     ai_msg: '在忙吗？方便的时候您回复下老师上面的问卷哈，帮助唐宁老师更了解你们哈',
      //   })
      // }, 15 * 60 * 1000)

    }
  }

  private static async updateLanguage(chatId: string, moerData: IMoerUserInfo) {
    const language = moerData.language
    if (language === 'english') {
      await chatStateStoreClient.update(chatId, { state:<IChattingFlag> { language: UserLanguage.Language_EN } })
    } else if (language === 'chinese') {
      await chatStateStoreClient.update(chatId, { state:<IChattingFlag> { language: UserLanguage.Language_ZH } })
    }
  }

  private static async updateCourseNo(chatId: string, moerData: IMoerUserInfo) {
    const courseNo = DataService.parseCourseNo(moerData)

    await chatDBClient.updateCourseNo(chatId, courseNo)
  }

  private static async updateMobileAndNationCode(chatId: string, moerData: IMoerUserInfo) {
    await chatStateStoreClient.update(chatId, {
      state:<IChattingFlag>{
        mobile: moerData.mobile,
        nation_code: moerData.nationcode
      }
    })
  }

  private static async sendMessage(chatId: string) {
    const language = await UserLanguage.getLanguage(chatId)
    const courseLink = await DataService.getCourseLink(0, chatId)
    const phoneNumber = await DataService.getMobile(chatId)
    let phoneNumberLast6: string = ''
    if (phoneNumber) {
      phoneNumberLast6 = phoneNumber.substring(phoneNumber.length - 6)
    }

    if (language === UserLanguage.Language_EN) {

      await yCloudMessageSender.sendById({
        chat_id: chatId,
        ai_msg: `Hi, nice to meet you!
Teacher Tangning's 5-day meditation practice is a free online program. It will start next Monday, with four consecutive days of sessions (8pm in Beijing, 5am in New York, 3pm in London, 4pm in Paris, 10pm in Sydney)，and a review session on Friday`,
        type: 'text',
      })


      await sleep(5000)
      await yCloudMessageSender.sendById({
        chat_id: chatId,
        ai_msg: `Your course access is all set！😊 Curious about what meditation actually is and how it can help youfeel better? Teacher Tangning’s got a quick 10-minute chat that’ll make it super clear—definitely check it out beforehand!

📚 Mini Class (Ocean Wave Stress Relief) ${courseLink}
(Log in on your phone: username ${phoneNumber}, default password ${phoneNumberLast6})`,
        type: 'text',
      })

    } else if (language === UserLanguage.Language_ZH) {

      await yCloudMessageSender.sendById({
        chat_id: chatId,
        ai_msg: `Hi，很高興遇見你！
唐寧老師的5天冥想練習是線上免費項目，會在下週一开始，連續4天的課程（北京時間 8pm，紐約時間 5am，倫敦時間 3pm，巴黎時間 4pm，悉尼時間 10pm），最後一天結營複習`,
        type: 'text',
      })

      await sleep(5000)
      await yCloudMessageSender.sendById({
        chat_id: chatId,
        ai_msg: `已經幫您開通好課程權限啦😊想知道冥想具體是什麼、怎麼幫到自己？唐寧老師用10分鐘給大家講清楚，一定提前看！

📚小講堂（海浪減壓）${courseLink}
（選擇手機登入，帳號${phoneNumber}, 密碼${phoneNumberLast6}）`,
        type: 'text',
      })

    }

    return ''
  }

}