import { Config } from 'config'
import { loadConfigByAccountName } from 'service/database/config'
import { wecomCommonMessageSender } from '../service/instance'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'

describe('Test', function () {
  beforeAll(async () => {
    // 设置测试环境
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe_online_test')
    Config.setting.localTest = false
  })

  it('spsp', async () => {
    await wecomCommonMessageSender.sendText('7881302572153624_1688857404698934', {
      text: '测试',
      description: '测试'
    })

    console.log('hi')
  }, 60000)
})