import { chatStateStoreClient } from '../../config/instance/base_instance'
import { DataService } from '../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from './context'
import { MetaActionRouter } from 'service/agent/meta_action/router'
import { PostAction } from './post_action'

export class AfterAdding extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return true
  }

  async getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return {
      '询问挖需第一题': PostAction.askQuestionOne,
      '询问挖需第二题': PostAction.askQuestionTwo,
      '测评股票段位为菜鸟闯荡生成评测结果': PostAction.askResultLevel1,
      '测评股票段位为小试牛刀生成评测结果': PostAction.askResultLevel2,
      '测评股票段位为进阶修炼生成评测结果': PostAction.askResultLevel3,
      '测评股票段位为老手精进生成评测结果': PostAction.askResultLevel4,
      '测评股票段位为理性操盘手生成评测结果': PostAction.askResultLevel5,
      '结束挖需并介绍高手的资金曲线': PostAction.sendEconomicCurve
    }
  }

  async getMetaAction(): Promise<Record<string, string>> { return MetaActions.afterAdding }

  async getThinkPrompt(): Promise<string> { return ThinkPrompt.afterAdding }
}

export class AfterBonding extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const chatState = await chatStateStoreClient.get(chatId)
    return await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1) && chatState.state.after_bonding as boolean
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      // '发送成功案例': PostAction.sendCaseImage,
      // '提供延期方案': PostAction.enterPostpone,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> { return Promise.resolve(MetaActions.afterBonding) }

  getThinkPrompt(): Promise<string> { return Promise.resolve(ThinkPrompt.afterBonding) }
}

export class AfterCourse1 extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return await DataService.isInCourseTimeLine(chatId, 'afterSales', 1)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      // '发送成功案例': PostAction.sendCaseImage,
      // '提供延期方案': PostAction.enterPostpone,
      '发送回放': AfterCourse1.sendCourseReplay,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> { return Promise.resolve(MetaActions.afterCourse1) }

  getThinkPrompt(): Promise<string> { return Promise.resolve(ThinkPrompt.afterCourse1) }
  private static async sendCourseReplay(chat_id: string) {
    const course1Backup = await DataService.getCourseLink(chat_id, 1) ?? ''
    const actionInfo: IActionInfo = { guidance: `给客户发送课程回放${course1Backup}`, }
    return actionInfo
  }
}

export class AfterCourse4 extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return await DataService.isInCourseTimeLine(chatId, 'afterSales', 4)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      // '发送成功案例': PostAction.sendCaseImage,
      '发起购买邀约': PostAction.sendInvitation,
      '保留名额': PostAction.reaskAnotherDay,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> { return Promise.resolve(MetaActions.afterCourse4) }

  getThinkPrompt(): Promise<string> { return Promise.resolve(ThinkPrompt.afterCourse4) }
}

export class AfterCourse6 extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return await DataService.isInCourseTimeLine(chatId, 'afterCourse', 6)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      // '强推陪跑营服务': PostAction.sendCourseIntro,
      // '发送成功案例': PostAction.sendCaseImage,
      '发起购买邀约': PostAction.sendInvitation,
      '提供定金方案': PostAction.provideDepositPlan,
      '保留名额': PostAction.reaskAnotherDay,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> { return Promise.resolve(MetaActions.afterCourse6) }

  getThinkPrompt(): Promise<string> { return Promise.resolve(ThinkPrompt.afterCourse6) }
}

export class DuringCourse extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return await DataService.isInCourseTimeLine(chatId, 'inCourse') && !await DataService.isInCourseTimeLine(chatId, 'afterSales')
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getMetaAction(): Promise<Record<string, string>> { return Promise.resolve(MetaActions.duringCourse) }

  getThinkPrompt(): Promise<string> { return Promise.resolve(ThinkPrompt.duringCourse) }
}

export const metaActionRouter = new MetaActionRouter([
  new DuringCourse(),
  new AfterCourse6(),
  new AfterCourse4(),
  new AfterCourse1(),
  new AfterBonding(),
  new AfterAdding(),
])  // 必须严格按照流程倒序添加