import { chatHistoryServiceClient } from '../../../service/instance'
import { RAG } from '../rag'
import { UUID } from 'lib/uuid/uuid'


describe('ragTest', () => {

  it('simpleRag', async () => {
    const inputQuery = '我想问下你们这个课程多少钱啊？'
    const strategy = ''

    chatHistoryServiceClient.getChatHistory = async (chat_id: string) => {
      return '我想问下你们这个课程多少钱啊？'
    }

    const res = await RAG.search(inputQuery, strategy, '123', UUID.v4())

    console.log('res', res)
  }, 9e8)

  it('multiQuestionRetrieve', async () => {
    const inputQuery = '我想问下你们这个课程多少钱啊？然后到时能不能分期？'
    const strategy = ''

    chatHistoryServiceClient.getChatHistory = async (chat_id: string) => {
      return '我想问下你们这个课程多少钱啊？然后到时能不能分期？'
    }

    const res = await RAG.search(inputQuery, strategy, '123', UUID.v4())

    console.log('res', res)
  }, 9e8)
})