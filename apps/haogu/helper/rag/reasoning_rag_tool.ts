import { SimpleRag } from './simple_rag'
import ElasticSearchService from 'model/elastic_search/elastic_search'


export interface IReasoningRagTool {
    name: string
    description: string
    execute: (params: RagToolExecuteParams) => Promise<string>
}

export interface RagToolExecuteParams {
    chatId: string
    roundId: string
    searchKey?: string
}

export class ReasoningRagTool {

  public static GeneralSearch = '常规rag搜索' //常规rag查询
  public static SearchStock = '搜索股票' //查询股票详情
  public static SearchBaseRule = '搜索基础规则' //查询基础规则


  public static async getTools(): Promise<IReasoningRagTool[]> {
    return [
      await this.getGeneralSearchTool(),
      await this.getSearchStockTool(),
      await this.getSearchBaseRuleTool()
    ]
  }

  public static async getToolByKey(key: string): Promise<IReasoningRagTool | null> {
    const toolMap = {
      [ReasoningRagTool.GeneralSearch]: await this.getGeneralSearchTool(),
      [ReasoningRagTool.SearchStock]: await this.getSearchStockTool(),
      [ReasoningRagTool.SearchBaseRule]: await this.getSearchBaseRuleTool()
    }

    return Promise.resolve(toolMap[key])
  }


  private static async getGeneralSearchTool() {
    const name = ReasoningRagTool.GeneralSearch
    const description = '一次只能搜索一个问题，复合问题需要多次搜索，入参：searchKey（待搜索的问题）'

    const execute = async (params: RagToolExecuteParams) => {
      // 获取要查询的文档
      const queryDocs = await SimpleRag.getQueryDocs(params.chatId)

      // 构建查询 filter
      const filter = {
        bool:{
          must:[
            {
              terms: {
                'metadata.doc': queryDocs
              }
            }
          ]
        }
      }

      const res = await ElasticSearchService.embeddingSearch(
        SimpleRag.index,
        params.searchKey ?? '',
        2,
        0.85,
        filter
      )

      if (res.length === 0) {
        return `${name}没有找到相关结果`
      } else {
        return `${name}搜索结果：
${res.map((item) => {
    return `问题：${item.metadata.q}
答案：${item.metadata.a}`
  }).join('\n')}`
      }
    }


    return {
      name:name,
      description: description,
      execute: execute
    } as IReasoningRagTool
  }


  private static async getSearchStockTool() {
    const name = ReasoningRagTool.SearchStock
    const description = '根据输入的股票名称，返回股票信息。入参：searchKey（待搜索的股票）'

    const execute = async (params: RagToolExecuteParams) => {
      return 'res'
    }

    return {
      name:name,
      description: description,
      execute: execute
    } as IReasoningRagTool
  }


  private static async getSearchBaseRuleTool() {
    const name = ReasoningRagTool.SearchBaseRule
    const description = '会返回一些聊天的基础规则，如果其他地方找不到答案可以使用基础规则'

    const execute = async (params: RagToolExecuteParams) => {
      return `- 禁止提供任何手机号，微信号，物流单号，账户等敏感信息
- 明确课程都是直播，禁止说录播或录制等字眼`
    }

    return {
      name:name,
      description: description,
      execute: execute
    } as IReasoningRagTool
  }




}