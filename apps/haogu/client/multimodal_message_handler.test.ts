/**
 * Haogu多模态消息处理器测试
 */

import { HaoguMultiModalMessageHandler } from './multimodal_message_handler'
import { IReceivedMessage, IWecomReceivedMsgType, IReceivedMessageSource } from 'model/juzi/type'
import { Config } from 'config'

// Mock dependencies
const mockChatDBClient = {
  isHumanInvolvement: jest.fn().mockResolvedValue(false)
}

const mockChatHistoryServiceClient = {
  getChatHistoryByChatId: jest.fn().mockResolvedValue([]),
  getUserMessageCount: jest.fn().mockResolvedValue(1),
  getMessageByMessageId: jest.fn().mockResolvedValue(null),
  addBotMessage: jest.fn().mockResolvedValue(undefined)
}

const mockChatStateStoreClient = {
  getFlags: jest.fn().mockResolvedValue({ is_friend_accepted: true })
}

const mockMessageReplyServiceClient = {
  reply: jest.fn().mockResolvedValue(undefined)
}

const mockEventTrackClient = {
  track: jest.fn()
}

// Mock Redis
jest.mock('model/redis/redis_cache', () => {
  return {
    RedisCacheDB: jest.fn().mockImplementation(() => ({
      addSet: jest.fn().mockResolvedValue(undefined),
      getSetMembers: jest.fn().mockResolvedValue([]),
      del: jest.fn().mockResolvedValue(undefined),
      set: jest.fn().mockResolvedValue(undefined),
      get: jest.fn().mockResolvedValue(null)
    }))
  }
})

// Mock BullMQ
jest.mock('bullmq', () => ({
  Queue: jest.fn().mockImplementation(() => ({
    add: jest.fn().mockResolvedValue(undefined)
  })),
  Worker: jest.fn().mockImplementation(() => ({}))
}))

// Mock JuziAPI
jest.mock('model/juzi/api', () => ({
  JuziAPI: {
    getOriginalImage: jest.fn().mockResolvedValue({
      code: 0,
      data: { url: 'https://example.com/image.jpg' }
    }),
    externalIdToWxId: jest.fn().mockResolvedValue('test-user-id'),
    getCustomerInfo: jest.fn().mockResolvedValue({
      name: 'Test User',
      createTimestamp: Date.now()
    })
  }
}))

// Mock LLM
jest.mock('lib/ai/llm/llm_model', () => ({
  LLM: jest.fn().mockImplementation(() => ({
    predictMessage: jest.fn().mockResolvedValue('这是一个股票K线图，显示了上涨趋势，建议关注支撑位。')
  }))
}))

// Mock Config
Config.setting = {
  wechatConfig: {
    id: 'test-bot-id',
    createdTime: new Date('2023-01-01')
  },
  waitingTime: {
    messageMerge: 5000
  }
} as any

Config.isTestAccount = jest.fn().mockReturnValue(true)

describe('HaoguMultiModalMessageHandler', () => {
  let handler: HaoguMultiModalMessageHandler

  beforeEach(() => {
    handler = new HaoguMultiModalMessageHandler(
      mockChatDBClient as any,
      mockChatHistoryServiceClient as any,
      mockChatStateStoreClient as any,
      mockMessageReplyServiceClient as any,
      mockEventTrackClient as any
    )

    // Clear all mocks
    jest.clearAllMocks()
  })

  describe('handle', () => {
    it('应该处理文本消息', async () => {
      const textMessage: IReceivedMessage = {
        messageId: 'msg-001',
        imContactId: 'user-001',
        messageType: IWecomReceivedMsgType.Text,
        timestamp: Date.now(),
        payload: {
          text: '请帮我分析一下这只股票'
        },
        isSelf: false,
        source: IReceivedMessageSource.WecomMessage,
        chatId: 'chat-001'
      }

      await handler.handle(textMessage)

      // 验证消息被添加到队列
      expect(handler['messageQueueBullMQ'].add).toHaveBeenCalledWith(
        'user-001',
        { userId: 'user-001', messageId: 'msg-001' },
        { delay: 5000, removeOnComplete: true, removeOnFail: true }
      )
    })

    it('应该处理图片消息', async () => {
      const imageMessage: IReceivedMessage = {
        messageId: 'msg-002',
        imContactId: 'user-001',
        messageType: IWecomReceivedMsgType.Image,
        timestamp: Date.now(),
        payload: {
          imageUrl: 'https://example.com/stock-chart.jpg'
        },
        isSelf: false,
        source: IReceivedMessageSource.WecomMessage,
        chatId: 'chat-001'
      }

      await handler.handle(imageMessage)

      expect(handler['messageQueueBullMQ'].add).toHaveBeenCalled()
    })

    it('应该忽略群消息', async () => {
      const groupMessage: IReceivedMessage = {
        messageId: 'msg-003',
        imContactId: 'user-001',
        messageType: IWecomReceivedMsgType.Text,
        timestamp: Date.now(),
        payload: { text: '群消息' },
        isSelf: false,
        source: IReceivedMessageSource.WecomMessage,
        chatId: 'chat-001',
        imRoomId: 'room-001' // 群消息标识
      }

      await handler.handle(groupMessage)

      expect(handler['messageQueueBullMQ'].add).not.toHaveBeenCalled()
    })

    it('应该忽略重复消息', async () => {
      const message: IReceivedMessage = {
        messageId: 'msg-004',
        imContactId: 'user-001',
        messageType: IWecomReceivedMsgType.Text,
        timestamp: Date.now(),
        payload: { text: '测试消息' },
        isSelf: false,
        source: IReceivedMessageSource.WecomMessage,
        chatId: 'chat-001'
      }

      // 第一次处理
      await handler.handle(message)
      expect(handler['messageQueueBullMQ'].add).toHaveBeenCalledTimes(1)

      // 第二次处理相同消息
      await handler.handle(message)
      expect(handler['messageQueueBullMQ'].add).toHaveBeenCalledTimes(1) // 不应该增加
    })
  })

  describe('getMultiModalContentFromMessages', () => {
    it('应该正确转换文本消息', async () => {
      const messages: IReceivedMessage[] = [{
        messageId: 'msg-001',
        imContactId: 'user-001',
        messageType: IWecomReceivedMsgType.Text,
        timestamp: Date.now(),
        payload: { text: '这是一个文本消息' },
        isSelf: false,
        source: IReceivedMessageSource.WecomMessage,
        chatId: 'chat-001'
      }]

      const result = await handler['getMultiModalContentFromMessages'](messages, 'user-001')

      expect(result).toHaveLength(1)
      expect(result[0].content).toEqual([
        { type: 'text', text: '这是一个文本消息' }
      ])
    })

    it('应该正确转换图片消息', async () => {
      const messages: IReceivedMessage[] = [{
        messageId: 'msg-002',
        imContactId: 'user-001',
        messageType: IWecomReceivedMsgType.Image,
        timestamp: Date.now(),
        payload: { imageUrl: 'https://example.com/image.jpg' },
        isSelf: false,
        source: IReceivedMessageSource.WecomMessage,
        chatId: 'chat-001'
      }]

      const result = await handler['getMultiModalContentFromMessages'](messages, 'user-001')

      expect(result).toHaveLength(1)
      expect(result[0].content).toEqual([
        { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } }
      ])
    })

    it('应该正确处理混合消息', async () => {
      const messages: IReceivedMessage[] = [
        {
          messageId: 'msg-001',
          imContactId: 'user-001',
          messageType: IWecomReceivedMsgType.Text,
          timestamp: Date.now(),
          payload: { text: '请分析这个图表' },
          isSelf: false,
          source: IReceivedMessageSource.WecomMessage,
          chatId: 'chat-001'
        },
        {
          messageId: 'msg-002',
          imContactId: 'user-001',
          messageType: IWecomReceivedMsgType.Image,
          timestamp: Date.now() + 1000,
          payload: { imageUrl: 'https://example.com/chart.jpg' },
          isSelf: false,
          source: IReceivedMessageSource.WecomMessage,
          chatId: 'chat-001'
        }
      ]

      const result = await handler['getMultiModalContentFromMessages'](messages, 'user-001')

      expect(result).toHaveLength(2)
      expect(result[0].content[0].type).toBe('text')
      expect(result[1].content[0].type).toBe('image_url')
    })

    it('应该忽略AI发送的消息', async () => {
      const messages: IReceivedMessage[] = [{
        messageId: 'msg-003',
        imContactId: 'user-001',
        messageType: IWecomReceivedMsgType.Text,
        timestamp: Date.now(),
        payload: { text: 'AI回复消息' },
        isSelf: false,
        source: IReceivedMessageSource.APIMessage, // AI发送的消息
        chatId: 'chat-001'
      }]

      const result = await handler['getMultiModalContentFromMessages'](messages, 'user-001')

      expect(result).toHaveLength(0)
    })
  })

  describe('replyWithMultiModalLLM', () => {
    it('应该使用GPT-5进行多模态分析', async () => {
      const processedMessages = [{
        messageId: 'msg-001',
        timestamp: Date.now(),
        content: [
          { type: 'text' as const, text: '请分析这个股票' },
          { type: 'image_url' as const, image_url: { url: 'https://example.com/chart.jpg' } }
        ],
        isUserMessage: true
      }]

      await handler['replyWithMultiModalLLM'](processedMessages, 'user-001')

      // 验证LLM被调用
      expect(require('lib/ai/llm/llm_model').LLM).toHaveBeenCalledWith({
        model: 'gpt-5',
        temperature: 0.1,
        maxTokens: 1000,
        meta: {
          promptName: 'haogu_multimodal_analysis',
          chat_id: 'chat_user-001',
          description: '股票多模态分析'
        }
      })

      // 验证回复被发送
      expect(mockMessageReplyServiceClient.reply).toHaveBeenCalledWith(
        ['这是一个股票K线图，显示了上涨趋势，建议关注支撑位。'],
        'user-001'
      )

      // 验证事件被记录
      expect(mockEventTrackClient.track).toHaveBeenCalledWith(
        'chat_user-001',
        '多模态分析',
        {
          message_count: 1,
          has_image: true,
          response_length: expect.any(Number)
        }
      )
    })

    it('应该在LLM失败时降级到文本处理', async () => {
      // Mock LLM失败
      const mockLLM = require('lib/ai/llm/llm_model').LLM
      mockLLM.mockImplementationOnce(() => ({
        predictMessage: jest.fn().mockRejectedValue(new Error('LLM调用失败'))
      }))

      const processedMessages = [{
        messageId: 'msg-001',
        timestamp: Date.now(),
        content: [
          { type: 'text' as const, text: '请分析这个股票' },
          { type: 'image_url' as const, image_url: { url: 'https://example.com/chart.jpg' } }
        ],
        isUserMessage: true
      }]

      await handler['replyWithMultiModalLLM'](processedMessages, 'user-001')

      // 验证降级到文本处理
      expect(mockMessageReplyServiceClient.reply).toHaveBeenCalledWith(
        ['请分析这个股票'],
        'user-001'
      )
    })
  })

  describe('isLatestMessage', () => {
    it('应该正确识别最新消息', async () => {
      const mockMessageStore = {
        getSetMembers: jest.fn().mockResolvedValue([
          { messageId: 'msg-001', timestamp: 1000 },
          { messageId: 'msg-002', timestamp: 2000 },
          { messageId: 'msg-003', timestamp: 3000 }
        ])
      }

      // Mock RedisCacheDB
      require('model/redis/redis_cache').RedisCacheDB.mockImplementationOnce(() => mockMessageStore)

      const isLatest = await handler['isLatestMessage']('user-001', 'msg-003')
      expect(isLatest).toBe(true)

      const isNotLatest = await handler['isLatestMessage']('user-001', 'msg-001')
      expect(isNotLatest).toBe(false)
    })
  })
})
