import { Queue, Worker } from 'bullmq'
import { LRUCache } from 'lru-cache'
import { Config } from 'config'
import { getChatId } from 'config/chat_id'
import { RedisCacheDB } from 'model/redis/redis_cache'
import {
  IReceivedImageMsg,
  IReceivedMessage,
  IReceivedMessageSource,
  IReceivedRecallMsg,
  IReceivedTextMsg,
  IReceivedVideoFileMsg,
  IReceivedVoiceMsg,
  IWecomReceivedMsgType
} from 'model/juzi/type'
import logger from 'model/logger/logger'
import { ChatHistoryService } from 'service/chat_history/chat_history'
import { sleep } from 'lib/schedule/schedule'
import { DateHelper } from 'lib/date/date'
import XunfeiASR from 'model/nls/xunfei'
import { JuziAPI } from 'model/juzi/api'
import { EventTracker, IEventType } from 'model/logger/data_driven'
import { catchError } from 'lib/error/catchError'
import { ChatInterruptHandler } from 'service/message_handler/interrupt/interrupt_handler'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { ChatDB } from 'service/database/chat'
import { MessageReplyService } from 'service/message_handler/reply/message_reply'
import { LLM } from 'lib/ai/llm/llm_model'
import { HumanMessage, SystemMessage } from '@langchain/core/messages'

/**
 * 多模态消息内容接口
 */
interface IMultiModalContent {
  type: 'text' | 'image_url'
  text?: string
  image_url?: { url: string }
}

/**
 * 处理后的消息接口
 */
interface IProcessedMessage {
  messageId: string
  timestamp: number
  content: IMultiModalContent[]
  isUserMessage: boolean
}

/**
 * haogu项目专用的多模态消息处理器
 * 支持将图片和文本消息合并后，直接使用GPT-5的多模态能力进行处理
 */
export class HaoguMultiModalMessageHandler {
  private readonly messageSet = new LRUCache<string, any>({ max: 3000 })
  private _messageQueueBullMQ: Queue
  private messageQueueWorker?: Worker
  private chatDBClient: ChatDB
  private chatHistoryServiceClient: ChatHistoryService
  private chatStateStoreClient: ChatStateStore
  private messageReplyServiceClient: MessageReplyService
  private eventTrackClient: EventTracker

  constructor(
    chatDBClient: ChatDB,
    chatHistoryServiceClient: ChatHistoryService,
    chatStateStoreClient: ChatStateStore,
    messageReplyServiceClient: MessageReplyService,
    eventTrackClient: EventTracker
  ) {
    this.chatDBClient = chatDBClient
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.chatStateStoreClient = chatStateStoreClient
    this.messageReplyServiceClient = messageReplyServiceClient
    this.eventTrackClient = eventTrackClient
  }

  private get messageQueueBullMQ(): Queue {
    if (!this._messageQueueBullMQ) {
      this._messageQueueBullMQ = new Queue(this.queueName, {
        connection: RedisCacheDB.getInstance()
      })
    }
    return this._messageQueueBullMQ
  }

  private getMessageStoreName(userId: string) {
    const chatId = getChatId(userId)
    return `haogu-multimodal-message-store_${chatId}`
  }

  private get queueName() {
    return `haogu-multimodal-message-queue_${Config.setting.wechatConfig!.id}`
  }

  /**
   * 处理接收到的消息
   */
  public async handle(message: IReceivedMessage) {
    if (!message.imContactId) {
      return
    }

    if (message.messageId && this.messageSet.has(message.messageId)) {
      return
    }

    // 不处理群消息
    if (message.roomWecomChatId || message.imRoomId) {
      return
    }

    if (message.payload && 'text' in message.payload && message.payload.text) {
      logger.debug('接收消息', message.payload.text, message.imContactId)
      // 文本消息，提前 increment 一下 version 来即时打断
      await ChatInterruptHandler.incrementChatVersion(getChatId(message.imContactId))
    }

    if (message.messageId) {
      this.messageSet.set(message.messageId, 1)
    }

    const senderId = message.imContactId
    const messageId = message.messageId
    if (!messageId) {
      return
    }

    // 如果是账号挂上之前的老客户不进行回复
    if (await this.isPastUser(message.imContactId)) {
      return
    }

    try {
      const messageStore = new RedisCacheDB(this.getMessageStoreName(senderId))
      // 将消息存到 Redis
      await messageStore.addSet(JSON.stringify(message))

      let delay = Config.setting.waitingTime.messageMerge
      if (Config.isTestAccount()) {
        delay = 5 * 1000
      } else if (message.isSelf) {
        delay = 0
      }

      // 添加消息到消息队列
      await this.messageQueueBullMQ.add(senderId, { userId: senderId, messageId }, { delay, removeOnComplete: true, removeOnFail: true })
    } catch (e) {
      logger.error('消息添加到任务队列失败', e)
    }
  }

  /**
   * 启动消息处理工作器
   */
  public startWorker() {
    if (!this.messageQueueWorker) {
      this.messageQueueWorker = new Worker(this.queueName, async (job) => {
        const { userId, messageId } = job.data

        // 只处理最新的消息
        const isLatest = await this.isLatestMessage(userId, messageId)
        if (!isLatest) {
          logger.debug(`跳过非最新消息: ${messageId}`)
          return
        }

        // 如果最后一条消息是 AI 消息，并跟现在的时间比较接近，则做下延迟处理
        const messages = await this.chatHistoryServiceClient.getChatHistoryByChatId(getChatId(userId))
        if (messages.length > 0) {
          const lastAIMessageReplyDiff = DateHelper.diff(messages[messages.length - 1].created_at, new Date(), 'second')
          if (lastAIMessageReplyDiff < 5) {
            await sleep(5 * 1000)
          }
        }

        await this.processUserMessages(userId)
      }, { connection: RedisCacheDB.getInstance(), concurrency: 20 })
    }
  }

  /**
   * 检查是否为最新消息
   */
  private async isLatestMessage(userId: string, messageId: string) {
    const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
    const messages = await messageStore.getSetMembers()

    if (!messages || messages.length === 0) {
      return false
    }

    // 消息有可能不是按顺序接收的，需要按时间重排序下
    messages.sort((msgA, msgB) => {
      return msgA.timestamp - msgB.timestamp
    })

    return messages[messages.length - 1].messageId === messageId
  }

  /**
   * 处理用户消息 - 多模态版本
   */
  public async processUserMessages(userId: string) {
    try {
      // 获取该客户在这段时间内发送的所有消息
      const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
      const userMessages = await messageStore.getSetMembers()

      // 消息有可能不是按顺序接收的，需要按时间重排序下
      userMessages.sort((msgA, msgB) => {
        return msgA.timestamp - msgB.timestamp
      })

      // 从消息队列中移除已处理的消息
      await messageStore.del()

      // 将消息转换为多模态内容
      const processedMessages = await this.getMultiModalContentFromMessages(userMessages, userId)

      if (processedMessages.length === 0) {
        return
      }

      // 使用多模态LLM进行回复
      await this.replyWithMultiModalLLM(processedMessages, userId)
    } catch (e) {
      console.error(`处理客户 ${userId} 的消息出错：`, e)
    }
  }

  /**
   * 将消息转换为多模态内容
   */
  private async getMultiModalContentFromMessages(messages: IReceivedMessage[], userId: string): Promise<IProcessedMessage[]> {
    const processedMessages: IProcessedMessage[] = []

    for (const message of messages) {
      try {
        if (message.messageId) {
          const cache = new RedisCacheDB(message.messageId)
          await cache.set(message, 3 * 60 * 60) // 缓存 3小时
        }
      } catch (e) {
        logger.warn('缓存消息失败')
      }

      try {
        const isSendByOpenAPI = message.source === IReceivedMessageSource.APIMessage
        if (isSendByOpenAPI) {
          // 忽略当前 AI 发送的消息
          continue
        }

        // 忽略系统通知
        if ([IWecomReceivedMsgType.SystemMessage, IWecomReceivedMsgType.WeChatWorkSystemMessage].includes(message.messageType)) {
          continue
        }

        if (message.coworker && !message.isSelf) { // 企业内员工发送的不进行处理
          break
        }

        // 忽略欢迎语
        if (message.messageType === IWecomReceivedMsgType.Text && await this.isFirstMessage(userId)) {
          const chatId = getChatId(userId)
          const flags = await this.chatStateStoreClient.getFlags<any>(chatId)
          if (!flags.is_friend_accepted) {
            // 处理欢迎语逻辑
            continue
          }
        }

        const content: IMultiModalContent[] = []

        switch (message.messageType) {
          case IWecomReceivedMsgType.Text: {
            const payload = message.payload as IReceivedTextMsg
            let text = payload.text
            if (payload.quoteMessage) {
              if (!payload.quoteMessage.content.text) {
                // 查询一下 messageId 对应的 chatHistory
                const chatHistory = await this.chatHistoryServiceClient.getMessageByMessageId(payload.quoteMessage.messageId)
                if (chatHistory && chatHistory.short_description) {
                  text = `对"${chatHistory.short_description}"的回复是：\n${payload.text}`
                } else {
                  text = `${payload.text}`
                }
              } else {
                text = `对"${payload.quoteMessage.content.text}"的回复是：\n${payload.text}`
              }
            }
            content.push({ type: 'text', text })
            break
          }

          case IWecomReceivedMsgType.Emoticon:
            content.push({ type: 'text', text: '【表情】' })
            break

          case IWecomReceivedMsgType.Voice: {
            const msg = message.payload as IReceivedVoiceMsg
            let text = ''
            if (msg.text) {
              text = msg.text
            } else {
              const xunfei = new XunfeiASR({
                appId: Config.setting.xunfeiASR.appId,
                secretKey: Config.setting.xunfeiASR.secretKey,
                uploadFileUrl: msg.voiceUrl
              })
              text = await xunfei.getResult()
            }
            content.push({ type: 'text', text })
            break
          }

          case IWecomReceivedMsgType.Image: {
            if (!message.isSelf && !message.coworker && !userId.startsWith('1688')) {
              const originalImageResponse = await JuziAPI.getOriginalImage(message.chatId, message.messageId as string)
              let originalImageUrl: string

              if (originalImageResponse.code !== 0) {
                originalImageUrl = (message.payload as IReceivedImageMsg).imageUrl
              } else {
                originalImageUrl = originalImageResponse.data.url
              }

              content.push({ type: 'image_url', image_url: { url: originalImageUrl } })
            }
            break
          }

          case IWecomReceivedMsgType.Unknown:
          case IWecomReceivedMsgType.VoiceOrVideoCall: {
            content.push({ type: 'text', text: '【语音/视频通话】' })
            break
          }

          default:
            logger.log(JSON.stringify(message, null, 4))
        }

        // 客服或者手机端客服侧人工回复的消息，不处理，但是需要存一下
        if (message.isSelf && message.source !== undefined && [IReceivedMessageSource.MobilePush, IReceivedMessageSource.TeamConsoleManual].includes(message.source)) {
          logger.log('人工回复', JSON.stringify(message, null, 4))
          const userId = await JuziAPI.externalIdToWxId(message.customerExternalUserId as string, Config.setting.wechatConfig?.id as string)
          const chatId = getChatId(userId as string)

          // 添加埋点，客户 + AI 回复
          if (content.length > 0) {
            const textContent = content.filter(c => c.type === 'text').map(c => c.text).join(' ')
            await this.chatHistoryServiceClient.addBotMessage(chatId, textContent, undefined, { is_send_by_human: true, message_id: message.messageId })
            this.eventTrackClient.track(chatId, IEventType.ManualReply, { reply: textContent, message: message.source })
          }
        } else if (content.length > 0) {
          processedMessages.push({
            messageId: message.messageId!,
            timestamp: message.timestamp,
            content,
            isUserMessage: true
          })
        }
      } catch (e) {
        // 避免消息处理过程中出错导致程序崩溃
        console.error('单条消息解析出错：', e)
      }
    }

    return processedMessages
  }

  /**
   * 使用多模态LLM进行回复
   */
  private async replyWithMultiModalLLM(processedMessages: IProcessedMessage[], userId: string) {
    if (processedMessages.length === 0) {
      return
    }

    const chatId = getChatId(userId)

    try {
      // 构建多模态消息内容
      const allContent: IMultiModalContent[] = []

      // 合并所有消息内容
      for (const processedMessage of processedMessages) {
        allContent.push(...processedMessage.content)
      }

      // 如果没有任何内容，直接返回
      if (allContent.length === 0) {
        return
      }

      // 构建系统提示词 - 针对股票分析场景
      const systemPrompt = `你是一名专业的股票分析师，擅长分析K线图、筹码分布图等股市技术图表。
当用户发送图片时，请仔细分析图片中的股票技术指标，包括但不限于：
- K线形态和趋势
- 成交量变化
- 技术指标（如MACD、RSI、KDJ等）
- 筹码分布情况
- 支撑位和阻力位

请根据图片内容和用户的问题，提供专业的股票分析建议。如果用户只发送了图片没有具体问题，请主动分析图片中的关键技术信息。`

      // 使用GPT-5进行多模态处理
      const llm = new LLM({
        model: 'gpt-5', // 使用GPT-5支持多模态
        temperature: 0.1,
        maxTokens: 1000,
        meta: {
          promptName: 'haogu_multimodal_analysis',
          chat_id: chatId,
          description: '股票多模态分析'
        }
      })

      // 构建消息
      const humanMessage = new HumanMessage({
        content: allContent
      })

      const messages = [
        new SystemMessage(systemPrompt),
        humanMessage
      ]

      // 调用LLM获取回复
      const response = await llm.predictMessage(messages)

      // 发送回复
      await this.messageReplyServiceClient.reply([response], userId)

      // 记录事件
      this.eventTrackClient.track(chatId, '多模态分析', {
        message_count: processedMessages.length,
        has_image: allContent.some(c => c.type === 'image_url'),
        response_length: response.length
      })

    } catch (e) {
      logger.error('多模态LLM回复失败', e)
      // 降级处理：如果多模态处理失败，转为文本处理
      const textContents = processedMessages
        .flatMap(msg => msg.content)
        .filter(content => content.type === 'text')
        .map(content => content.text)
        .filter(text => text && text.trim().length > 0)

      if (textContents.length > 0) {
        await this.messageReplyServiceClient.reply(textContents, userId)
      }
    }
  }

  private async isFirstMessage(userId: string) {
    const chatId = getChatId(userId)
    return await this.chatHistoryServiceClient.getUserMessageCount(chatId) === 0
  }

  private async isPastUser(senderId: string) {
    try {
      const accountCreatedTime = Config.setting.wechatConfig?.createdTime // 挂号时间

      if (!accountCreatedTime) {
        return false
      }

      if (senderId === Config.setting.wechatConfig?.id) {
        return false
      }

      const userInfo = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, senderId)
      if (!userInfo) {
        return false
      }

      if (['麦子', 'SYQ'].includes(userInfo.name)) {
        return false
      }

      if ((userInfo && Number(userInfo.createTimestamp) < accountCreatedTime.getTime())) {
        logger.warn({ chat_id: getChatId(senderId) }, `客户在 ${accountCreatedTime.toLocaleString()} 之前创建`, senderId)
        return true
      }

      return false
    } catch (e) {
      return false
    }
  }
}
