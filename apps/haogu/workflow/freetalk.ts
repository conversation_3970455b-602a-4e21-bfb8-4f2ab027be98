import logger from 'model/logger/logger'
import { ContextBuilder } from './context'
import { getPrompt } from 'service/agent/prompt'
import { IWorkflowState } from 'service/llm/state'
import { LLM } from 'lib/ai/llm/llm_model'
import { Node } from './nodes/types'
import { trackInvoke, WorkFlowNode } from './nodes/base_node'

import { chatHistoryServiceClient } from '../service/base_instance'
import { freeThinkClient, replyClient } from '../service/send_message_instance'
import { metaActionRouter } from '../meta_action/meta_action_router'

export class FreeTalk extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const metaActionStage = await metaActionRouter.getThinkAndMetaActions(state.chat_id)
    const contextComponent = new ContextBuilder({ state })
    const { action, strategy } = await freeThinkClient.invoke(
      state,
      metaActionStage.thinkPrompt,
      metaActionStage.metaActions,
      await contextComponent.customerBehavior(state.chat_id),
      await contextComponent.customerPortrait(state.chat_id),
      await contextComponent.temporalInformation(state.chat_id),
    )

    const actionInfo = await metaActionRouter.handleAction(state.chat_id, state.round_id, action)
    const talkStrategyPrompt = [
      '无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止',
      strategy,
      actionInfo.guidance
    ].filter(Boolean).join('\n')

    // build the context
    const composerPrompt = await getPrompt('free-compose')
    const dialogHistory = await chatHistoryServiceClient.getChatHistory(state.chat_id, 3, 9)
    const output = await LLM.predict(
      composerPrompt, {
        model: 'gpt-5-mini',
        responseJSON: true,
        meta: {
          promptName: 'composer',
          chat_id: state.chat_id,
          round_id: state.round_id,
        } }, {
        dialogHistory: dialogHistory,
        talkStrategy: strategy,
      })

    let module: string[] = []
    try {
      const parsedOutput = JSON.parse(output)
      module = parsedOutput.module
    } catch (error) {
      logger.error('Composer 解析 JSON 失败:', error)
    }
    const context = await ContextBuilder.build({
      state,
      courseConfig: module.includes('课程设置'),
      retrievedKnowledge: module.includes('补充知识'),
      customerMemory: module.includes('客户记忆'),
      customerBehavior: module.includes('客户行为'),
      customerPortrait: module.includes('客户画像'),
      temporalInformation: module.includes('时间信息'),
      talkStrategyPrompt: talkStrategyPrompt,
    })

    await replyClient.invoke({
      state,
      model: 'gpt-5-chat',
      context: context,
      temperature: 0.8,
      maxTokens: 400,
      promptName: 'free_talk',
      postReplyCallBack: actionInfo.callback
    })
    return Node.FreeTalk
  }
}