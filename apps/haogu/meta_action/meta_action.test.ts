import { MetaActionRouter } from 'service/agent/meta_action/router'

import { AfterAdding } from './stages/after_adding'
import { AfterBonding } from './stages/after_bonding'
import { AfterCourse1 } from './stages/after_course1'
import { AfterCourse2 } from './stages/after_course2'
import { AfterCourse3 } from './stages/after_course3'
import { AfterPaid } from './stages/after_paid'
import { DuringCourse } from './stages/during_course'

const components = [
  new AfterPaid(),
  new DuringCourse(),
  new AfterCourse3(),
  new AfterCourse2(),
  new AfterCourse1(),
  new AfterBonding(),
  new AfterAdding(),
]  // 必须严格按照流程倒序添加

describe('metaActionTest', () => {

  it('testGetMetaAction', async () => {
    // DataService.getCurrentTime = async () => {
    //   return {
    //     day: 3,
    //     time: '23:38:00',
    //   }
    // }
    // DataService.isPaidSystemCourse = async () => {
    //   return true
    // }
    const chat_id = '7881299950922845_1688858213716953'
    const metaActionRouter = new MetaActionRouter(components)
    const metaActionStage = await metaActionRouter.getThinkAndMetaActions(chat_id, '')
    console.log(await metaActionRouter.getStageName(chat_id))
    console.log(metaActionStage.thinkPrompt)
    console.log(metaActionStage.metaActions)
  }, 9e8)

  it('测试MetaAction', async () => {
    // DataService.getCurrentTime = async () => {
    //   return {
    //     day: 4,
    //     time: '23:38:00',
    //   }
    // }
    // DataService.isPaidSystemCourse = async () => {
    //   return false
    // }
    const metaActionRouter = new MetaActionRouter(components)
    const metaActionStage = await metaActionRouter.getThinkAndMetaActions('7881299950922845_1688858213716953', '')
    console.log(metaActionStage.thinkPrompt)
    console.log(metaActionStage.metaActions)
    console.log(await metaActionRouter.handleAction('chatId', 'roundId', ['强推陪跑营服务']))
  }, 9e8)

  // it('刷新延期状态', async () => {
  //   const expData = await PrismaMongoClient.getInstance().chat.findMany({
  //     where: {
  //       course_no_ori: { isSet: true }
  //     }
  //   })
  //
  //   // const expData = await PrismaMongoClient.getInstance().chat.findMany({
  //   //   where: {
  //   //     id: '7881300516060552_1688857404698934'
  //   //   }
  //   // })
  //
  //   for (const item of expData) {
  //     console.log(item.id)
  //     const chatState = await chatStateStoreClient.get(item.id)
  //     chatState.state.has_postpone = true
  //     await PrismaMongoClient.getInstance().chat.update({
  //       where: {
  //         id: item.id
  //       },
  //       data: {
  //         chat_state: chatState
  //       }
  //     })
  //   }
  // }, 9e8)
  //
  // it('TestGetNewCourseStartDayDiff', async () => {
  //   DataService.getCurrentTime = async () => {
  //     return {
  //       day: 13,
  //       time: '23:38:00',
  //     }
  //   }
  //   console.log(await PostAction.getNewCourseStartDayDiff('7881299950922845_1688858213716953'))
  // }, 60000)
})