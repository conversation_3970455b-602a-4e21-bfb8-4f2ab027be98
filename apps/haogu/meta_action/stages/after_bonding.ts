import { chatStateStoreClient } from '../../service/instance'
import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { IChattingFlag } from '../../state/user_flags'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class AfterBonding extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    const chatState = await chatStateStoreClient.get(chatId)
    const afterBonding = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1) && (chatState.state as IChattingFlag).after_bonding as boolean
    return Promise.resolve(afterBonding)
  }

  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      // '发送成功案例': PostAction.sendCaseImage,
      // '提供延期方案': PostAction.enterPostpone,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterBonding)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterBonding)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve()
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }
}