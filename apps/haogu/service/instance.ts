import { ChatDB } from 'service/database/chat'
import { PrismaMongoClient } from '../database/prisma'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { ChatHistoryService } from 'service/chat_history/chat_history'
import { EventTracker } from 'model/logger/data_driven'
import { WecomMessageSender } from 'service/message_handler/juzi/message_sender'
import { MemoryStore } from 'service/memory/memory_store'
import { LLMReply } from 'service/llm/llm_reply'
import { SendMessageResultHandler } from 'service/message_handler/juzi/send_result_handler'
import { HaoguVisualizedSopProcessor } from '../visualized_sop/visualized_sop_processor'
import { HaoguVisualizedGroupSopProcessor } from '../visualized_sop/visualized_group_sop_processor'
import { BaseHumanTransfer } from 'service/human_transfer/human_transfer'
import { WecomCommonMessageSender } from 'service/visualized_sop/common_sender/wecom'
import { FreeThink } from 'service/agent/freethink'
import { RA<PERSON><PERSON>elper } from 'service/rag/rag'
import { Reply } from 'service/agent/reply'

export const chatDBClient = new ChatDB(PrismaMongoClient.getCommonInstance())
export const chatStateStoreClient = new ChatStateStore(chatDBClient)
export const chatHistoryServiceClient = new ChatHistoryService(PrismaMongoClient.getCommonInstance(), chatStateStoreClient)
export const eventTrackClient = new EventTracker(PrismaMongoClient.getCommonInstance())
export const wecomMessageSender = new WecomMessageSender(chatHistoryServiceClient)
export const memoryStoreClient = new MemoryStore(chatHistoryServiceClient, chatStateStoreClient)
export const llmReplyClient = new LLMReply(chatDBClient, chatHistoryServiceClient)
export const sendMessageResultHandlerClient = new SendMessageResultHandler(chatHistoryServiceClient)
export const wecomCommonMessageSender = new WecomCommonMessageSender(wecomMessageSender, chatHistoryServiceClient)
export const haoguVisualizedSopProcessor = new HaoguVisualizedSopProcessor('haogu', chatDBClient, chatHistoryServiceClient, wecomCommonMessageSender)
export const haoguVisualizedGroupSopProcessor = new HaoguVisualizedGroupSopProcessor('haogu', PrismaMongoClient.getCommonInstance(), wecomMessageSender)
export const humanTransferClient = new BaseHumanTransfer(chatDBClient, chatStateStoreClient)
export const freeThinkClient = new FreeThink(chatHistoryServiceClient, eventTrackClient)
export const ragHelperClient = new RAGHelper(chatHistoryServiceClient)
export const replyClient = new Reply(chatDBClient, chatHistoryServiceClient)