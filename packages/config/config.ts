import { DevelopConfigure } from './develop'
import { ProductConfigure } from './prod'
import { IConfig } from './interface'
import * as process from 'node:process'

export enum MoerAccountType {
  Normal = 0,
  XiaoHongShu = 1,
  BaoDing = 2
}

export enum YuHeAccountType {
  ZhongShenTong  = 'Zhongshentong',
  ZhiCheng = 'Zhicheng'
}


//TODO:修改为兼容多apps的功能
export class Config {
  private static _setting: IConfig | undefined

  public static get setting(): IConfig {
    if (!this._setting) {
      let config
      const env = process.env.NODE_ENV as 'prod' | 'dev' | string
      if (env === 'prod') {
        config = ProductConfigure
      } else if (env === 'dev') {
        config = DevelopConfigure
      } else {
        config = DevelopConfigure // 默认使用开发环境配置
      }

      this._setting = {
        // 加入启动 bot 时候的环境变量
        ...config,
        AGENT_ID: process.env.AGENT_ID ?? '',
        AGENT_NAME: process.env.AGENT_NAME ?? '老师',
        CREATE_AT: process.env.CREATE_AT ?? '',
        onlyReceiveMessage: process.env.ONLY_RECEIVE_MESSAGE === 'true',
      }

      // 本地测试开发，设置下 localTest 参数
      if (process.env.NODE_ENV === 'test') {
        (this._setting as IConfig).localTest = true
      }
    }

    return this._setting as IConfig
  }

  public static isDev() {
    const env = process.env.NODE_ENV as 'prod' | 'dev' | string
    return env !== 'prod'
  }

  // 判断是否为内部员工
  public static isInternalMember(userId: string) {
    return ['****************', '****************', '****************', '****************', '****************', '****************', '****************'].includes(userId)
  }

  public static isTestAccount() {
    return ['****************', '****************', '****************'].includes(Config.setting.wechatConfig?.id as string)
  }

  public static isLocalTestAccount() {
    return ['****************'].includes(Config.setting.wechatConfig?.id as string)
  }

  // 线上测试账号
  public static isOnlineTestAccount() {
    return ['****************'].includes(Config.setting.wechatConfig?.id as string)
  }

  static isGroupOwner() {
    return Boolean(Config.setting.wechatConfig?.isGroupOwner)
  }

  static getAccountType() {
    if (['****************'].includes(Config.setting.wechatConfig?.id as string)) {
      return MoerAccountType.XiaoHongShu
    }

    if (['****************'].includes(Config.setting.wechatConfig?.id as string)) {
      return MoerAccountType.BaoDing
    }

    return MoerAccountType.Normal
  }

  static isXiaoHongShuAccount() {
    return this.getAccountType() === MoerAccountType.XiaoHongShu
  }

  static isBaoDingAccount() {
    return this.getAccountType() === MoerAccountType.BaoDing
  }

  static isOYuanChannel() {
    return this.getAccountType() === MoerAccountType.XiaoHongShu || this.getAccountType() === MoerAccountType.BaoDing
  }

  static getYuHeAccountType(account:string = Config.setting.wechatConfig?.id ?? '') {
    if (['****************', '****************', '****************', '****************', '****************', '****************', '****************'].includes(account)) {
      return YuHeAccountType.ZhiCheng
    }

    return YuHeAccountType.ZhongShenTong
  }
}