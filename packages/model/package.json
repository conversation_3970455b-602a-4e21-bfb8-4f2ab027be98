{"name": "model", "private": true, "scripts": {"tsc-check": "tsc --noEmit", "prisma_generate": "cd prisma && npx prisma generate"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^20.7.0", "prisma": "^6.13.0", "typescript": "5.8.2"}, "dependencies": {"@alicloud/pop-core": "^1.8.0", "@elastic/elasticsearch": "^8.13.1", "@langchain/community": "^0.3.50", "@langchain/core": "^0.3.70", "@prisma/client": "^6.13.0", "@typegoose/typegoose": "^12.8.0", "alibabacloud-nls": "^1.0.2", "axios": "^1.5.1", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "config": "workspace:*", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "fast-xml-parser": "^4.3.5", "ioredis": "~5.4.1", "lib": "workspace:*", "mongoose": "^8.16.0", "openai": "^4.98.0", "pickleparser": "^0.2.1", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "redlock": "5.0.0-beta.2", "reflect-metadata": "^0.2.2"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=18"}}